export interface Service {
  id: string;
  name: string;
  description: string;
  shortDescription: string;
  images: string[];
  price: number;
  duration: number;
  category: string;
  capacity: number;
  availableSlots: TimeSlot[];
  features: string[];
}

export interface TimeSlot {
  id: string;
  date: string;
  time: string;
  available: boolean;
  spotsLeft: number;
}

export interface Reservation {
  id: string;
  serviceId: string;
  serviceName: string;
  date: string;
  time: string;
  participants: number;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  totalPrice: number;
  status: 'confirmed' | 'pending' | 'cancelled';
  createdAt: string;
}

export interface Employee {
  id: string;
  name: string;
  role: string;
  email: string;
  avatar: string;
  services: string[];
}

export interface AdminUser {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'manager' | 'employee';
  avatar: string;
  permissions: string[];
}

export interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  status: 'active' | 'inactive';
  totalBookings: number;
  totalSpent: number;
  firstVisit: string;
  lastVisit: string;
  favoriteServices: {
    name: string;
    bookings: number;
  }[];
  bookingHistory: {
    id: string;
    serviceName: string;
    date: string;
    amount: number;
    status: 'completed' | 'upcoming' | 'cancelled';
  }[];
  notes?: string;
}

export interface BookingFormData {
  serviceId: string;
  date: string;
  time: string;
  participants: number;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  notes?: string;
}

export interface PaymentFormData {
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardholderName: string;
  billingAddress: {
    street: string;
    city: string;
    postalCode: string;
    country: string;
  };
}
