import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const serviceId = params.id

    // Get scheduling rules for the service
    const { data: rules, error } = await supabase
      .from('service_scheduling_rules')
      .select('*')
      .eq('service_id', serviceId)
      .eq('is_active', true)
      .order('created_at', { ascending: true })

    if (error) {
      console.error('Error fetching scheduling rules:', error)
      return NextResponse.json(
        { error: 'Failed to fetch scheduling rules' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: rules || []
    })

  } catch (error) {
    console.error('Error in scheduling rules API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
