import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { withAdminAuth, logAdminAction } from '@/lib/admin-auth'
import { Database } from '@/lib/types/database'

type Employee = Database['public']['Tables']['employees']['Row']
type EmployeeUpdate = Database['public']['Tables']['employees']['Update']

// GET /api/admin/employees/[id] - Get single employee with full details
export const GET = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
  try {
    const employeeId = params.id

    const { data: employee, error } = await supabaseAdmin
      .from('employees')
      .select(`
        *,
        employee_availability (
          id,
          day_of_week,
          start_time,
          end_time,
          is_available,
          effective_from,
          effective_until
        ),
        employee_service_qualifications (
          id,
          qualification_level,
          certified_date,
          expiry_date,
          notes,
          is_active,
          service:services (
            id,
            name,
            category,
            image_url
          )
        ),
        employee_time_off (
          id,
          start_date,
          end_date,
          start_time,
          end_time,
          reason,
          type,
          status
        ),
        reservations:reservations!reservations_assigned_employee_id_fkey (
          id,
          reservation_number,
          start_time,
          end_time,
          status,
          total_amount,
          participant_count,
          service:services (
            id,
            name,
            category
          ),
          customer_feedback (
            rating,
            staff_rating,
            review_text
          )
        ),
        employee_analytics (
          total_assignments,
          completed_assignments,
          total_revenue_generated,
          average_customer_rating
        )
      `)
      .eq('id', employeeId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Employee not found' }, { status: 404 })
      }
      console.error('Error fetching employee:', error)
      return NextResponse.json({ error: 'Failed to fetch employee' }, { status: 500 })
    }

    // Calculate additional statistics
    const reservations = employee.reservations || []
    const qualifications = employee.employee_service_qualifications || []
    const availability = employee.employee_availability || []
    const timeOff = employee.employee_time_off || []
    
    const upcomingReservations = reservations.filter(r => 
      new Date(r.start_time) > new Date() && r.status !== 'cancelled'
    )
    
    const thisMonthReservations = reservations.filter(r => {
      const reservationDate = new Date(r.start_time)
      const now = new Date()
      return reservationDate.getMonth() === now.getMonth() && 
             reservationDate.getFullYear() === now.getFullYear()
    })

    // Calculate performance metrics
    const completedReservations = reservations.filter(r => r.status === 'completed')
    const totalRevenue = completedReservations.reduce((sum, r) => sum + (r.total_amount || 0), 0)
    
    const ratings = completedReservations
      .flatMap(r => r.customer_feedback || [])
      .map(f => f.staff_rating || f.rating)
      .filter(rating => rating !== null)
    
    const averageRating = ratings.length > 0 
      ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length 
      : 0

    // Service specializations
    const serviceStats = completedReservations.reduce((acc, r) => {
      const serviceName = r.service?.name || 'Unknown'
      if (!acc[serviceName]) {
        acc[serviceName] = { count: 0, revenue: 0 }
      }
      acc[serviceName].count++
      acc[serviceName].revenue += r.total_amount || 0
      return acc
    }, {} as Record<string, { count: number, revenue: number }>)

    const topServices = Object.entries(serviceStats)
      .sort(([, a], [, b]) => b.count - a.count)
      .slice(0, 5)
      .map(([name, stats]) => ({ name, ...stats }))

    // Availability summary
    const availabilityByDay = availability.reduce((acc, avail) => {
      const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
      const dayName = dayNames[avail.day_of_week]
      acc[dayName] = {
        available: avail.is_available,
        startTime: avail.start_time,
        endTime: avail.end_time
      }
      return acc
    }, {} as Record<string, any>)

    // Upcoming time off
    const upcomingTimeOff = timeOff.filter(timeOff => 
      new Date(timeOff.start_date) > new Date() && timeOff.status === 'approved'
    )

    return NextResponse.json({
      employee: {
        ...employee,
        stats: {
          totalAssignments: reservations.length,
          completedAssignments: completedReservations.length,
          upcomingAssignments: upcomingReservations.length,
          thisMonthAssignments: thisMonthReservations.length,
          totalRevenue,
          averageRating: Math.round(averageRating * 100) / 100,
          totalRatings: ratings.length,
          activeQualifications: qualifications.filter(q => q.is_active).length,
          upcomingTimeOff: upcomingTimeOff.length
        },
        performance: {
          topServices,
          monthlyTrend: [], // Could be calculated from historical data
          customerSatisfaction: averageRating,
          utilizationRate: 0 // Would need capacity data to calculate
        },
        schedule: {
          weeklyAvailability: availabilityByDay,
          upcomingTimeOff: upcomingTimeOff.map(timeOff => ({
            id: timeOff.id,
            startDate: timeOff.start_date,
            endDate: timeOff.end_date,
            reason: timeOff.reason,
            type: timeOff.type
          }))
        }
      }
    })
  } catch (error) {
    console.error('Employee GET error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'employees:read')

// PUT /api/admin/employees/[id] - Update single employee
export const PUT = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
  try {
    const employeeId = params.id
    const updates: EmployeeUpdate = await request.json()

    // Get current employee for audit log
    const { data: currentEmployee } = await supabaseAdmin
      .from('employees')
      .select('*')
      .eq('id', employeeId)
      .single()

    if (!currentEmployee) {
      return NextResponse.json({ error: 'Employee not found' }, { status: 404 })
    }

    // Update employee
    const { data: updatedEmployee, error } = await supabaseAdmin
      .from('employees')
      .update(updates)
      .eq('id', employeeId)
      .select()
      .single()

    if (error) {
      console.error('Error updating employee:', error)
      return NextResponse.json({ error: 'Failed to update employee' }, { status: 500 })
    }

    // Log admin action
    await logAdminAction(
      user.id,
      'UPDATE',
      'employees',
      employeeId,
      currentEmployee,
      updatedEmployee,
      request
    )

    return NextResponse.json({ employee: updatedEmployee })
  } catch (error) {
    console.error('Employee PUT error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'employees:write')

// DELETE /api/admin/employees/[id] - Deactivate single employee
export const DELETE = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
  try {
    const employeeId = params.id

    // Get employee for audit log
    const { data: employeeToDeactivate } = await supabaseAdmin
      .from('employees')
      .select('*')
      .eq('id', employeeId)
      .single()

    if (!employeeToDeactivate) {
      return NextResponse.json({ error: 'Employee not found' }, { status: 404 })
    }

    // Check for future reservations assigned to this employee
    const { data: futureReservations } = await supabaseAdmin
      .from('reservations')
      .select('id, start_time, service:services(name)')
      .eq('assigned_employee_id', employeeId)
      .gte('start_time', new Date().toISOString())

    if (futureReservations && futureReservations.length > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot deactivate employee with future reservations',
          futureReservations: futureReservations.map(r => ({
            id: r.id,
            startTime: r.start_time,
            serviceName: r.service?.name
          }))
        },
        { status: 400 }
      )
    }

    // Soft delete by setting is_active to false
    const { error } = await supabaseAdmin
      .from('employees')
      .update({ 
        is_active: false, 
        is_available_for_scheduling: false 
      })
      .eq('id', employeeId)

    if (error) {
      console.error('Error deactivating employee:', error)
      return NextResponse.json({ error: 'Failed to deactivate employee' }, { status: 500 })
    }

    // Log admin action
    await logAdminAction(
      user.id,
      'DEACTIVATE',
      'employees',
      employeeId,
      employeeToDeactivate,
      { ...employeeToDeactivate, is_active: false },
      request
    )

    return NextResponse.json({ 
      message: 'Employee deactivated successfully',
      employeeId
    })
  } catch (error) {
    console.error('Employee DELETE error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'employees:write')
