import { logAdminAction, withAdminAuth } from '@/lib/admin-auth'
import { supabaseAdmin } from '@/lib/supabase'
import { Database } from '@/lib/types/database'
import { NextRequest, NextResponse } from 'next/server'

type Service = Database['public']['Tables']['services']['Row']
type ServiceInsert = Database['public']['Tables']['services']['Insert']
type ServiceUpdate = Database['public']['Tables']['services']['Update']

// GET /api/admin/services - List all services with admin details
export const GET = withAdminAuth(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search')
    const category = searchParams.get('category')
    const isActive = searchParams.get('active')
    const offset = (page - 1) * limit

    // Build query
    let query = supabaseAdmin
      .from('services')
      .select(`
        *,
        pricing_tiers (
          id,
          tier_name,
          price,
          min_age,
          max_age,
          is_active
        ),
        service_equipment_requirements (
          id,
          capacity_per_participant,
          equipment (
            id,
            name,
            total_capacity
          )
        ),
        service_scheduling_rules (
          id,
          day_of_week,
          operating_start_time,
          operating_end_time,
          booking_interval_minutes,
          specific_times,
          max_bookings_per_day,
          is_active
        ),
        service_blackout_dates (
          id,
          start_date,
          end_date,
          reason,
          is_active
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    // Apply filters
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`)
    }
    if (category && category !== 'all') {
      query = query.eq('category', category)
    }
    if (isActive !== null) {
      query = query.eq('is_active', isActive === 'true')
    }

    const { data: services, error, count } = await query

    if (error) {
      console.error('Error fetching services:', error)
      return NextResponse.json({ error: 'Failed to fetch services' }, { status: 500 })
    }

    // Get total count for pagination
    const { count: totalCount } = await supabaseAdmin
      .from('services')
      .select('*', { count: 'exact', head: true })

    return NextResponse.json({
      services: services || [],
      pagination: {
        page,
        limit,
        total: totalCount || 0,
        totalPages: Math.ceil((totalCount || 0) / limit)
      }
    })
  } catch (error) {
    console.error('Services GET error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'services:read')

// POST /api/admin/services - Create new service
export const POST = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const serviceData: ServiceInsert = await request.json()

    // Validate required fields
    if (!serviceData.name || !serviceData.duration_minutes || !serviceData.base_price || !serviceData.max_participants) {
      return NextResponse.json(
        { error: 'Missing required fields: name, duration_minutes, base_price, max_participants' },
        { status: 400 }
      )
    }

    // Create service
    const { data: service, error } = await supabaseAdmin
      .from('services')
      .insert(serviceData)
      .select()
      .single()

    if (error) {
      console.error('Error creating service:', error)
      return NextResponse.json({ error: 'Failed to create service' }, { status: 500 })
    }

    // Log admin action
    await logAdminAction(
      user.id,
      'CREATE',
      'services',
      service.id,
      null,
      service,
      request
    )

    return NextResponse.json({ service }, { status: 201 })
  } catch (error) {
    console.error('Service POST error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'services:write')

// PUT /api/admin/services - Bulk update services
export const PUT = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const { serviceIds, updates }: { serviceIds: string[], updates: ServiceUpdate } = await request.json()

    if (!serviceIds || serviceIds.length === 0) {
      return NextResponse.json({ error: 'No service IDs provided' }, { status: 400 })
    }

    // Get current services for audit log
    const { data: currentServices } = await supabaseAdmin
      .from('services')
      .select('*')
      .in('id', serviceIds)

    // Update services
    const { data: updatedServices, error } = await supabaseAdmin
      .from('services')
      .update(updates)
      .in('id', serviceIds)
      .select()

    if (error) {
      console.error('Error updating services:', error)
      return NextResponse.json({ error: 'Failed to update services' }, { status: 500 })
    }

    // Log admin actions
    for (const service of updatedServices || []) {
      const oldService = currentServices?.find(s => s.id === service.id)
      await logAdminAction(
        user.id,
        'UPDATE',
        'services',
        service.id,
        oldService,
        service,
        request
      )
    }

    return NextResponse.json({ 
      services: updatedServices,
      updated: updatedServices?.length || 0
    })
  } catch (error) {
    console.error('Services PUT error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'services:write')

// DELETE /api/admin/services - Bulk delete services
export const DELETE = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const { serviceIds }: { serviceIds: string[] } = await request.json()

    if (!serviceIds || serviceIds.length === 0) {
      return NextResponse.json({ error: 'No service IDs provided' }, { status: 400 })
    }

    // Get services for audit log before deletion
    const { data: servicesToDelete } = await supabaseAdmin
      .from('services')
      .select('*')
      .in('id', serviceIds)

    // Check for existing reservations
    const { data: existingReservations } = await supabaseAdmin
      .from('reservations')
      .select('id, service_id')
      .in('service_id', serviceIds)
      .gte('start_time', new Date().toISOString())

    if (existingReservations && existingReservations.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete services with future reservations' },
        { status: 400 }
      )
    }

    // Soft delete by setting is_active to false instead of hard delete
    const { error } = await supabaseAdmin
      .from('services')
      .update({ is_active: false })
      .in('id', serviceIds)

    if (error) {
      console.error('Error deleting services:', error)
      return NextResponse.json({ error: 'Failed to delete services' }, { status: 500 })
    }

    // Log admin actions
    for (const service of servicesToDelete || []) {
      await logAdminAction(
        user.id,
        'DELETE',
        'services',
        service.id,
        service,
        null,
        request
      )
    }

    return NextResponse.json({ 
      deleted: serviceIds.length,
      message: 'Services deactivated successfully'
    })
  } catch (error) {
    console.error('Services DELETE error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'services:delete')
