import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const serviceId = searchParams.get('serviceId')

    // Get all services if no specific service ID
    if (!serviceId) {
      const { data: services, error: servicesError } = await supabase
        .from('services')
        .select('id, name')
        .eq('is_active', true)
        .limit(5)

      if (servicesError) {
        console.error('Error fetching services:', servicesError)
        return NextResponse.json({ error: 'Failed to fetch services' }, { status: 500 })
      }

      return NextResponse.json({
        success: true,
        message: 'Available services for testing',
        services: services || []
      })
    }

    // Get scheduling rules for specific service
    const { data: rules, error: rulesError } = await supabase
      .from('service_scheduling_rules')
      .select('*')
      .eq('service_id', serviceId)
      .eq('is_active', true)

    if (rulesError) {
      console.error('Error fetching scheduling rules:', rulesError)
      return NextResponse.json({ error: 'Failed to fetch scheduling rules' }, { status: 500 })
    }

    // Get service details
    const { data: service, error: serviceError } = await supabase
      .from('services')
      .select('id, name, duration_minutes, max_participants')
      .eq('id', serviceId)
      .single()

    if (serviceError) {
      console.error('Error fetching service:', serviceError)
      return NextResponse.json({ error: 'Service not found' }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: `Scheduling rules for service: ${service.name}`,
      service,
      rules: rules || [],
      rulesCount: rules?.length || 0
    })

  } catch (error) {
    console.error('Test scheduling rules error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
