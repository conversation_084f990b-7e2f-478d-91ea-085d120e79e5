import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { withAdminAuth, logAdminAction } from '@/lib/admin-auth'
import { Database } from '@/lib/types/database'

type Equipment = Database['public']['Tables']['equipment']['Row']
type EquipmentInsert = Database['public']['Tables']['equipment']['Insert']
type EquipmentUpdate = Database['public']['Tables']['equipment']['Update']

// GET /api/admin/equipment - List all equipment with utilization data
export const GET = withAdminAuth(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search')
    const isActive = searchParams.get('active')
    const offset = (page - 1) * limit

    // Build query
    let query = supabaseAdmin
      .from('equipment')
      .select(`
        *,
        service_equipment_requirements (
          id,
          capacity_per_participant,
          service:services (
            id,
            name,
            category,
            is_active
          )
        ),
        equipment_reservations (
          id,
          reserved_capacity,
          status,
          created_at,
          time_slot_id
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    // Apply filters
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`)
    }
    if (isActive !== null) {
      query = query.eq('is_active', isActive === 'true')
    }

    const { data: equipment, error } = await query

    if (error) {
      console.error('Error fetching equipment:', error)
      return NextResponse.json({ error: 'Failed to fetch equipment' }, { status: 500 })
    }

    // Calculate utilization statistics
    const enhancedEquipment = equipment?.map(item => {
      const reservations = item.equipment_reservations || []
      const services = item.service_equipment_requirements?.map(req => req.service) || []
      
      // Calculate utilization for the last 30 days
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      
      const recentReservations = reservations.filter(r => 
        new Date(r.created_at) >= thirtyDaysAgo
      )
      
      const totalReservedCapacity = recentReservations.reduce((sum, r) => sum + r.reserved_capacity, 0)
      const maxPossibleCapacity = item.total_capacity * 30 // Assuming daily usage
      const utilizationRate = maxPossibleCapacity > 0 ? (totalReservedCapacity / maxPossibleCapacity) * 100 : 0

      return {
        ...item,
        stats: {
          totalReservations: reservations.length,
          recentReservations: recentReservations.length,
          utilizationRate: Math.round(utilizationRate * 100) / 100,
          associatedServices: services.filter(s => s?.is_active).length,
          currentlyReserved: reservations.filter(r => r.status === 'reserved').length
        },
        associatedServices: services.filter(s => s?.is_active)
      }
    })

    // Get total count for pagination
    const { count: totalCount } = await supabaseAdmin
      .from('equipment')
      .select('*', { count: 'exact', head: true })

    return NextResponse.json({
      equipment: enhancedEquipment || [],
      pagination: {
        page,
        limit,
        total: totalCount || 0,
        totalPages: Math.ceil((totalCount || 0) / limit)
      }
    })
  } catch (error) {
    console.error('Equipment GET error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'equipment:read')

// POST /api/admin/equipment - Create new equipment
export const POST = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const equipmentData: EquipmentInsert = await request.json()

    // Validate required fields
    if (!equipmentData.name || !equipmentData.total_capacity) {
      return NextResponse.json(
        { error: 'Missing required fields: name, total_capacity' },
        { status: 400 }
      )
    }

    // Create equipment
    const { data: equipment, error } = await supabaseAdmin
      .from('equipment')
      .insert(equipmentData)
      .select()
      .single()

    if (error) {
      console.error('Error creating equipment:', error)
      return NextResponse.json({ error: 'Failed to create equipment' }, { status: 500 })
    }

    // Log admin action
    await logAdminAction(
      user.id,
      'CREATE',
      'equipment',
      equipment.id,
      null,
      equipment,
      request
    )

    return NextResponse.json({ equipment }, { status: 201 })
  } catch (error) {
    console.error('Equipment POST error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'equipment:write')

// PUT /api/admin/equipment - Bulk update equipment
export const PUT = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const { equipmentIds, updates }: { equipmentIds: string[], updates: EquipmentUpdate } = await request.json()

    if (!equipmentIds || equipmentIds.length === 0) {
      return NextResponse.json({ error: 'No equipment IDs provided' }, { status: 400 })
    }

    // Get current equipment for audit log
    const { data: currentEquipment } = await supabaseAdmin
      .from('equipment')
      .select('*')
      .in('id', equipmentIds)

    // Update equipment
    const { data: updatedEquipment, error } = await supabaseAdmin
      .from('equipment')
      .update(updates)
      .in('id', equipmentIds)
      .select()

    if (error) {
      console.error('Error updating equipment:', error)
      return NextResponse.json({ error: 'Failed to update equipment' }, { status: 500 })
    }

    // Log admin actions
    for (const equipment of updatedEquipment || []) {
      const oldEquipment = currentEquipment?.find(e => e.id === equipment.id)
      await logAdminAction(
        user.id,
        'UPDATE',
        'equipment',
        equipment.id,
        oldEquipment,
        equipment,
        request
      )
    }

    return NextResponse.json({ 
      equipment: updatedEquipment,
      updated: updatedEquipment?.length || 0
    })
  } catch (error) {
    console.error('Equipment PUT error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'equipment:write')

// DELETE /api/admin/equipment - Deactivate equipment
export const DELETE = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const { equipmentIds }: { equipmentIds: string[] } = await request.json()

    if (!equipmentIds || equipmentIds.length === 0) {
      return NextResponse.json({ error: 'No equipment IDs provided' }, { status: 400 })
    }

    // Get equipment for audit log before deactivation
    const { data: equipmentToDeactivate } = await supabaseAdmin
      .from('equipment')
      .select('*')
      .in('id', equipmentIds)

    // Check for active reservations
    const { data: activeReservations } = await supabaseAdmin
      .from('equipment_reservations')
      .select('id, equipment_id')
      .in('equipment_id', equipmentIds)
      .eq('status', 'reserved')

    if (activeReservations && activeReservations.length > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot deactivate equipment with active reservations',
          activeReservations: activeReservations.length
        },
        { status: 400 }
      )
    }

    // Check for service dependencies
    const { data: serviceDependencies } = await supabaseAdmin
      .from('service_equipment_requirements')
      .select('id, equipment_id, service:services(name, is_active)')
      .in('equipment_id', equipmentIds)

    const activeServiceDependencies = serviceDependencies?.filter(dep => dep.service?.is_active) || []
    
    if (activeServiceDependencies.length > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot deactivate equipment required by active services',
          affectedServices: activeServiceDependencies.map(dep => dep.service?.name)
        },
        { status: 400 }
      )
    }

    // Soft delete by setting is_active to false
    const { error } = await supabaseAdmin
      .from('equipment')
      .update({ is_active: false })
      .in('id', equipmentIds)

    if (error) {
      console.error('Error deactivating equipment:', error)
      return NextResponse.json({ error: 'Failed to deactivate equipment' }, { status: 500 })
    }

    // Log admin actions
    for (const equipment of equipmentToDeactivate || []) {
      await logAdminAction(
        user.id,
        'DEACTIVATE',
        'equipment',
        equipment.id,
        equipment,
        { ...equipment, is_active: false },
        request
      )
    }

    return NextResponse.json({ 
      deactivated: equipmentIds.length,
      message: 'Equipment deactivated successfully'
    })
  } catch (error) {
    console.error('Equipment DELETE error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'equipment:write')
