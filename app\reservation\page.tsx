"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { AnimatePresence, motion } from "framer-motion";
import {
	CalendarIcon,
	Check,
	ChevronLeft,
	ChevronRight,
	Clock,
	CreditCard,
	Euro,
	Loader2,
	Minus,
	Plus,
	Users,
	X,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

import { Calendar } from "@/components/ui/calendar";
import { CustomerInfo, ParticipantInfo } from "@/lib/types";
import { useSearchParams } from "next/navigation";

import { ServiceWithPricing, TimeSlotWithAvailability } from "@/lib/types";

// Updated types using proper backend types
interface BookingItem {
	service: ServiceWithPricing;
	date: string;
	timeSlot: TimeSlotWithAvailability;
	participants: ParticipantInfo[];
	totalPrice: number;
}

interface FormData extends CustomerInfo {
	specialRequests: string;
}

// Fetch real availability data from API
const fetchTimeSlots = async (
	serviceId: string,
	date: string,
	participantCount: number = 1
): Promise<TimeSlotWithAvailability[]> => {
	try {
		const response = await fetch(
			`/api/services/${serviceId}/availability?date=${date}&participants=${participantCount}`
		);
		if (!response.ok) throw new Error("Failed to fetch availability");
		const data = await response.json();

		return data.data.timeSlots || [];
	} catch (error) {
		console.error("Error fetching time slots:", error);
		return [];
	}
};

const steps = [
	{ id: 1, title: "Services", description: "Choisissez vos excursions" },
	{ id: 2, title: "Date & Heure", description: "Sélectionnez vos créneaux" },
	{ id: 3, title: "Participants", description: "Nombre de personnes" },
	{ id: 4, title: "Informations", description: "Vos coordonnées" },
	{ id: 5, title: "Confirmation", description: "Finaliser la réservation" },
];

export default function ReservationPage() {
	const [currentStep, setCurrentStep] = useState(1);
	const [services, setServices] = useState<ServiceWithPricing[]>([]);
	const [selectedService, setSelectedService] = useState<string | null>(null);
	const [bookingItems, setBookingItems] = useState<BookingItem[]>([]);
	const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
	const [timeSlots, setTimeSlots] = useState<TimeSlotWithAvailability[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const [servicesLoading, setServicesLoading] = useState(true);
	const [formData, setFormData] = useState<FormData>({
		firstName: "",
		lastName: "",
		email: "",
		phone: "",
		specialRequests: "",
	});
	const [formErrors, setFormErrors] = useState<Partial<FormData>>({});

	const searchParams = useSearchParams();
	const preSelectedServiceId = searchParams.get("service");

	// Fetch services from database
	useEffect(() => {
		const fetchServices = async () => {
			try {
				const response = await fetch("/api/services");
				if (!response.ok) throw new Error("Failed to fetch services");
				const data = await response.json();
				setServices(data.services);

				// Handle entry point detection
				if (preSelectedServiceId) {
					// Coming from service detail page - pre-select service and skip to step 2
					const foundService = data.services.find((s: ServiceWithPricing) => s.id === preSelectedServiceId);
					if (foundService) {
						setSelectedService(preSelectedServiceId);
						setCurrentStep(2);
					}
				}
			} catch (error) {
				console.error("Error fetching services:", error);
			} finally {
				setServicesLoading(false);
			}
		};

		fetchServices();
	}, [preSelectedServiceId]);

	// Supprimer la fonction generateDates car le composant Calendar gère la plage

	// Load time slots when date changes
	useEffect(() => {
		if (selectedDate && selectedService) {
			const loadTimeSlots = async () => {
				const serviceSlots = await fetchTimeSlots(selectedService, selectedDate.toISOString().split("T")[0], 1);
				setTimeSlots(serviceSlots);
			};

			loadTimeSlots();
		}
	}, [selectedDate, selectedService]);

	const toggleService = (serviceId: string) => {
		setSelectedServices((prev) =>
			prev.includes(serviceId) ? prev.filter((id) => id !== serviceId) : [...prev, serviceId]
		);
	};

	const addBookingItem = (timeSlotTime: string) => {
		if (!selectedService) return;

		const service = services.find((s) => s.id === selectedService);
		const timeSlot = timeSlots.find((slot) => {
			const slotTime = new Date(slot.start_time).toLocaleTimeString("fr-FR", {
				hour: "2-digit",
				minute: "2-digit",
			});
			return slotTime === timeSlotTime;
		});
		if (!service || !selectedDate || !timeSlot) return;

		// Calculate base price from pricing tiers
		const basePrice = service.pricing_tiers?.[0]?.price || 0;

		const newItem: BookingItem = {
			service,
			date: selectedDate.toISOString().split("T")[0],
			timeSlot,
			participants: [{ firstName: "", lastName: "", age: 25 }], // Initialize with one participant
			totalPrice: basePrice,
		};

		setBookingItems([newItem]); // Only one booking item since we only allow one service
	};

	const removeBookingItem = (index: number) => {
		setBookingItems((prev) => prev.filter((_, i) => i !== index));
	};

	const updateParticipants = (index: number, participants: ParticipantInfo[]) => {
		setBookingItems((prev) =>
			prev.map((item, i) => {
				if (i === index) {
					const basePrice = item.service.pricing_tiers?.[0]?.price || 0;
					return { ...item, participants, totalPrice: basePrice * participants.length };
				}
				return item;
			})
		);
	};

	const getTotalPrice = () => {
		return bookingItems.reduce((total, item) => total + item.totalPrice, 0);
	};

	const validateForm = (): boolean => {
		const errors: Partial<FormData> = {};

		if (!formData.firstName.trim()) errors.firstName = "Prénom requis";
		if (!formData.lastName.trim()) errors.lastName = "Nom requis";
		if (!formData.email.trim()) errors.email = "Email requis";
		else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) errors.email = "Email invalide";
		if (!formData.phone.trim()) errors.phone = "Téléphone requis";

		setFormErrors(errors);
		return Object.keys(errors).length === 0;
	};

	const handleSubmit = async () => {
		if (!validateForm()) return;

		setIsLoading(true);

		try {
			// Create bookings for each booking item
			const bookingPromises = bookingItems.map(async (item) => {
				const bookingData = {
					serviceId: item.service.id,
					timeSlotId: item.timeSlot.id,
					participants: item.participants,
					customerInfo: {
						firstName: formData.firstName,
						lastName: formData.lastName,
						email: formData.email,
						phone: formData.phone,
					},
					specialRequests: formData.specialRequests || undefined,
				};

				const response = await fetch("/api/bookings", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify(bookingData),
				});

				if (!response.ok) {
					const error = await response.json();
					throw new Error(error.error || "Failed to create booking");
				}

				return await response.json();
			});

			const results = await Promise.all(bookingPromises);

			// All bookings successful - redirect to confirmation
			const firstBookingId = results[0]?.data?.reservationId;
			window.location.href = `/reservation/confirmation?booking=${firstBookingId}`;
		} catch (error) {
			console.error("Booking error:", error);
			setIsLoading(false);
			alert(
				`Erreur lors de la création de la réservation: ${
					error instanceof Error ? error.message : "Erreur inconnue"
				}`
			);
		}
	};

	const canProceedToNextStep = () => {
		switch (currentStep) {
			case 1:
				return selectedServices.length > 0;
			case 2:
				return bookingItems.length > 0;
			case 3:
				return bookingItems.every(
					(item) =>
						item.participants.length > 0 &&
						item.participants.every((p) => p.firstName && p.lastName && p.age > 0)
				);
			case 4:
				return formData.firstName && formData.lastName && formData.email && formData.phone;
			default:
				return true;
		}
	};

	const formatDate = (dateString: string) => {
		const date = new Date(dateString);
		return date.toLocaleDateString("fr-FR", {
			weekday: "long",
			year: "numeric",
			month: "long",
			day: "numeric",
		});
	};

	return (
		<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50">
			{/* Header */}
			<header className="bg-white/90 backdrop-blur-md shadow-sm sticky top-0 z-50">
				<div className="container mx-auto px-4 py-4">
					<div className="flex items-center justify-between">
						<Link href="/" className="flex items-center space-x-3">
							<Image
								src="/images/logo-hd.png"
								alt="Soleil & Découverte"
								width={60}
								height={60}
								className="object-contain"
							/>
							<div>
								<h1 className="text-2xl font-bold bg-gradient-to-r from-orange-500 to-yellow-500 bg-clip-text text-transparent">
									Soleil & Découverte
								</h1>
								<p className="text-sm text-emerald-600">Réservation en ligne</p>
							</div>
						</Link>

						<Link href="/">
							<Button variant="ghost" size="sm">
								<X className="w-4 h-4 mr-2" />
								Fermer
							</Button>
						</Link>
					</div>
				</div>
			</header>

			{/* Progress Steps */}
			<div className="bg-white border-b">
				<div className="container mx-auto px-4 py-4">
					<div className="flex items-center justify-between">
						{steps.map((step, index) => (
							<div key={step.id} className="flex items-center">
								<div
									className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-semibold ${
										currentStep >= step.id
											? "bg-emerald-500 text-white"
											: "bg-gray-200 text-gray-500"
									}`}
								>
									{currentStep > step.id ? <Check className="w-4 h-4" /> : step.id}
								</div>
								<div className="ml-2 hidden sm:block">
									<div
										className={`text-sm font-medium ${
											currentStep >= step.id ? "text-emerald-600" : "text-gray-500"
										}`}
									>
										{step.title}
									</div>
									<div className="text-xs text-gray-400">{step.description}</div>
								</div>
								{index < steps.length - 1 && (
									<ChevronRight className="w-4 h-4 text-gray-300 mx-2 hidden sm:block" />
								)}
							</div>
						))}
					</div>
				</div>
			</div>

			<div className="container mx-auto px-4 py-8">
				<div className="max-w-4xl mx-auto">
					<AnimatePresence mode="wait">
						{/* Step 1: Service Selection */}
						{currentStep === 1 && (
							<motion.div
								key="step1"
								initial={{ opacity: 0, x: 50 }}
								animate={{ opacity: 1, x: 0 }}
								exit={{ opacity: 0, x: -50 }}
								transition={{ duration: 0.3 }}
							>
								<div className="text-center mb-8">
									<h2 className="text-3xl font-bold text-gray-900 mb-4">Choisissez vos excursions</h2>
									<p className="text-gray-600">
										Sélectionnez une ou plusieurs activités pour votre aventure
									</p>
								</div>

								<div className="grid md:grid-cols-2 gap-6 mb-8">
									{servicesLoading ? (
										<div className="col-span-2 flex items-center justify-center py-12">
											<Loader2 className="w-8 h-8 animate-spin text-emerald-600" />
											<span className="ml-3 text-emerald-600 font-medium">
												Chargement des services...
											</span>
										</div>
									) : (
										services.map((service) => (
											<motion.div
												key={service.id}
												whileHover={{ y: -5 }}
												whileTap={{ scale: 0.98 }}
											>
												<Card
													className={`cursor-pointer transition-all duration-300 ${
														selectedServices.includes(service.id)
															? "ring-2 ring-emerald-500 bg-emerald-50"
															: "hover:shadow-lg"
													}`}
													onClick={() => toggleService(service.id)}
												>
													<div className="relative">
														<Image
															src={service.image_url || "/placeholder.svg"}
															alt={service.name}
															width={400}
															height={200}
															className="w-full h-48 object-cover rounded-t-lg"
														/>
														{selectedServices.includes(service.id) && (
															<div className="absolute top-4 right-4 bg-emerald-500 text-white rounded-full p-2">
																<Check className="w-4 h-4" />
															</div>
														)}
														{preSelectedServiceId &&
															preSelectedServiceId === service.id && (
																<div className="absolute top-4 left-4 bg-orange-500 text-white rounded-full px-3 py-1 text-sm font-semibold">
																	Recommandé
																</div>
															)}
														<div className="absolute bottom-4 left-4">
															<Badge className="bg-white/90 text-emerald-700 font-semibold">
																{service.ageLimit}
															</Badge>
														</div>
													</div>

													<CardContent className="p-6">
														<h3 className="text-xl font-bold text-gray-900 mb-2">
															{service.name}
														</h3>
														<p className="text-gray-600 mb-4 text-sm">
															{service.description}
														</p>

														<div className="flex items-center gap-4 mb-4 text-sm text-gray-500">
															<div className="flex items-center">
																<Clock className="w-4 h-4 mr-1" />
																{Math.floor(service.duration_minutes / 60)}h
																{service.duration_minutes % 60 > 0
																	? ` ${service.duration_minutes % 60}min`
																	: ""}
															</div>
															<div className="flex items-center">
																<Users className="w-4 h-4 mr-1" />
																Max {service.max_participants}
															</div>
															<div className="flex items-center">
																<Euro className="w-4 h-4 mr-1" />
																{service.pricing_tiers?.[0]?.price || 0}€
															</div>
														</div>

														<div className="space-y-1">
															{(service.features || [])
																.slice(0, 2)
																.map((feature, idx) => (
																	<div
																		key={idx}
																		className="flex items-center text-sm text-emerald-600"
																	>
																		<div className="w-2 h-2 bg-emerald-400 rounded-full mr-2"></div>
																		{feature}
																	</div>
																))}
														</div>
													</CardContent>
												</Card>
											</motion.div>
										))
									)}
								</div>
							</motion.div>
						)}

						{/* Step 2: Date & Time Selection */}
						{currentStep === 2 && (
							<motion.div
								key="step2"
								initial={{ opacity: 0, x: 50 }}
								animate={{ opacity: 1, x: 0 }}
								exit={{ opacity: 0, x: -50 }}
								transition={{ duration: 0.3 }}
							>
								<div className="text-center mb-8">
									<h2 className="text-3xl font-bold text-gray-900 mb-4">Choisissez votre date</h2>
									<p className="text-gray-600">Sélectionnez une date puis vos créneaux horaires</p>
								</div>

								{/* Date Selection */}
								<Card className="mb-8">
									<CardHeader>
										<CardTitle className="flex items-center gap-2">
											<CalendarIcon className="w-5 h-5" />
											Sélectionnez une date
										</CardTitle>
									</CardHeader>
									<CardContent className="flex justify-center">
										<Calendar
											mode="single"
											selected={selectedDate}
											onSelect={setSelectedDate}
											initialFocus
											fromDate={new Date()}
											toDate={new Date(new Date().setFullYear(new Date().getFullYear() + 1))} // Un an à partir d'aujourd'hui
											disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))} // Désactiver les dates passées
											className="rounded-md border"
										/>
									</CardContent>
								</Card>

								{/* Time Slot Selection */}
								{selectedDate && (
									<motion.div
										initial={{ opacity: 0, y: 20 }}
										animate={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.3 }}
									>
										<div className="space-y-6">
											{selectedServices.map((serviceId) => {
												const service = services.find((s) => s.id === serviceId);
												const slots = timeSlots[serviceId] || [];

												return (
													<Card key={serviceId}>
														<CardHeader>
															<CardTitle className="flex items-center gap-2">
																<Clock className="w-5 h-5" />
																{service?.name}
															</CardTitle>
															<p className="text-sm text-gray-600">
																{formatDate(selectedDate.toISOString().split("T")[0])} à
																Choisissez un créneau
															</p>
														</CardHeader>
														<CardContent>
															<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-3">
																{slots.map((slot) => {
																	const slotTime = new Date(
																		slot.start_time
																	).toLocaleTimeString("fr-FR", {
																		hour: "2-digit",
																		minute: "2-digit",
																	});
																	return (
																		<Button
																			key={slot.id}
																			variant="outline"
																			disabled={!slot.is_available}
																			className={`h-auto p-3 flex flex-col ${
																				!slot.is_available
																					? "opacity-50 cursor-not-allowed"
																					: "hover:bg-emerald-50 hover:border-emerald-300"
																			}`}
																			onClick={() =>
																				slot.is_available &&
																				addBookingItem(serviceId, slotTime)
																			}
																		>
																			<div className="text-lg font-bold">
																				{slotTime}
																			</div>
																			<div className="text-xs text-gray-500">
																				{slot.is_available
																					? `${slot.available_capacity} places`
																					: "Complet"}
																			</div>
																		</Button>
																	);
																})}
															</div>
														</CardContent>
													</Card>
												);
											})}
										</div>
									</motion.div>
								)}

								{/* Selected Bookings */}
								{bookingItems.length > 0 && (
									<motion.div
										initial={{ opacity: 0, y: 20 }}
										animate={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.3 }}
										className="mt-8"
									>
										<Card>
											<CardHeader>
												<CardTitle>Vos réservations sélectionnées</CardTitle>
											</CardHeader>
											<CardContent>
												<div className="space-y-4">
													{bookingItems.map((item, index) => (
														<div
															key={index}
															className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
														>
															<div>
																<h4 className="font-semibold">{item.service.name}</h4>
																<p className="text-sm text-gray-600">
																	{formatDate(item.date)} à{" "}
																	{new Date(
																		item.timeSlot.start_time
																	).toLocaleTimeString("fr-FR", {
																		hour: "2-digit",
																		minute: "2-digit",
																	})}
																</p>
															</div>
															<Button
																variant="ghost"
																size="sm"
																onClick={() => removeBookingItem(index)}
															>
																<X className="w-4 h-4" />
															</Button>
														</div>
													))}
												</div>
											</CardContent>
										</Card>
									</motion.div>
								)}
							</motion.div>
						)}

						{/* Step 3: Participants */}
						{currentStep === 3 && (
							<motion.div
								key="step3"
								initial={{ opacity: 0, x: 50 }}
								animate={{ opacity: 1, x: 0 }}
								exit={{ opacity: 0, x: -50 }}
								transition={{ duration: 0.3 }}
							>
								<div className="text-center mb-8">
									<h2 className="text-3xl font-bold text-gray-900 mb-4">Nombre de participants</h2>
									<p className="text-gray-600">
										Indiquez le nombre de personnes pour chaque excursion
									</p>
								</div>

								<div className="space-y-6">
									{bookingItems.map((item, index) => (
										<Card key={index}>
											<CardContent className="p-6">
												<div className="flex items-center gap-4 mb-4">
													<Image
														src={item.service.image_url || "/placeholder.svg"}
														alt={item.service.name}
														width={80}
														height={80}
														className="rounded-lg object-cover"
													/>
													<div className="flex-1">
														<h3 className="text-lg font-semibold">{item.service.name}</h3>
														<p className="text-sm text-gray-600">
															{formatDate(item.date)} à{" "}
															{new Date(item.timeSlot.start_time).toLocaleTimeString(
																"fr-FR",
																{
																	hour: "2-digit",
																	minute: "2-digit",
																}
															)}
														</p>
														<p className="text-sm text-emerald-600">
															{item.service.min_age
																? `À partir de ${item.service.min_age} ans`
																: "Tous âges"}
														</p>
													</div>
												</div>

												<div className="flex items-center justify-between">
													<div className="flex items-center gap-4">
														<Label className="text-sm font-medium">Participants:</Label>
														<div className="flex items-center gap-2">
															<Button
																variant="outline"
																size="sm"
																onClick={() => {
																	const newParticipants = [...item.participants];
																	if (newParticipants.length > 1) {
																		newParticipants.pop();
																		updateParticipants(index, newParticipants);
																	}
																}}
																disabled={item.participants.length <= 1}
															>
																<Minus className="w-4 h-4" />
															</Button>
															<span className="w-12 text-center font-semibold">
																{item.participants.length}
															</span>
															<Button
																variant="outline"
																size="sm"
																onClick={() => {
																	const newParticipants = [
																		...item.participants,
																		{ firstName: "", lastName: "", age: 25 },
																	];
																	if (
																		newParticipants.length <=
																		parseInt(item.service.capacity)
																	) {
																		updateParticipants(index, newParticipants);
																	}
																}}
																disabled={
																	item.participants.length >=
																	parseInt(item.service.capacity)
																}
															>
																<Plus className="w-4 h-4" />
															</Button>
														</div>
													</div>
													<div className="text-right">
														<div className="text-sm text-gray-500">
															{item.service.price}€ × {item.participants.length}
														</div>
														<div className="text-lg font-bold text-emerald-600">
															{item.totalPrice}€
														</div>
													</div>
												</div>
											</CardContent>
										</Card>
									))}

									<Card className="bg-emerald-50 border-emerald-200">
										<CardContent className="p-6">
											<div className="flex justify-between items-center">
												<span className="text-lg font-semibold">Total</span>
												<span className="text-2xl font-bold text-emerald-600">
													{getTotalPrice()}€
												</span>
											</div>
										</CardContent>
									</Card>
								</div>
							</motion.div>
						)}

						{/* Step 4: Personal Information */}
						{currentStep === 4 && (
							<motion.div
								key="step4"
								initial={{ opacity: 0, x: 50 }}
								animate={{ opacity: 1, x: 0 }}
								exit={{ opacity: 0, x: -50 }}
								transition={{ duration: 0.3 }}
							>
								<div className="text-center mb-8">
									<h2 className="text-3xl font-bold text-gray-900 mb-4">Vos informations</h2>
									<p className="text-gray-600">Renseignez vos coordonnées pour la réservation</p>
								</div>

								<Card>
									<CardContent className="p-6">
										<div className="grid md:grid-cols-2 gap-6">
											<div>
												<Label htmlFor="firstName">Prénom *</Label>
												<Input
													id="firstName"
													value={formData.firstName}
													onChange={(e) =>
														setFormData((prev) => ({ ...prev, firstName: e.target.value }))
													}
													className={formErrors.firstName ? "border-red-500" : ""}
												/>
												{formErrors.firstName && (
													<p className="text-red-500 text-sm mt-1">{formErrors.firstName}</p>
												)}
											</div>

											<div>
												<Label htmlFor="lastName">Nom *</Label>
												<Input
													id="lastName"
													value={formData.lastName}
													onChange={(e) =>
														setFormData((prev) => ({ ...prev, lastName: e.target.value }))
													}
													className={formErrors.lastName ? "border-red-500" : ""}
												/>
												{formErrors.lastName && (
													<p className="text-red-500 text-sm mt-1">{formErrors.lastName}</p>
												)}
											</div>

											<div>
												<Label htmlFor="email">Email *</Label>
												<Input
													id="email"
													type="email"
													value={formData.email}
													onChange={(e) =>
														setFormData((prev) => ({ ...prev, email: e.target.value }))
													}
													className={formErrors.email ? "border-red-500" : ""}
												/>
												{formErrors.email && (
													<p className="text-red-500 text-sm mt-1">{formErrors.email}</p>
												)}
											</div>

											<div>
												<Label htmlFor="phone">Téléphone *</Label>
												<Input
													id="phone"
													value={formData.phone}
													onChange={(e) =>
														setFormData((prev) => ({ ...prev, phone: e.target.value }))
													}
													className={formErrors.phone ? "border-red-500" : ""}
													placeholder="+33 6 XX XX XX XX"
												/>
												{formErrors.phone && (
													<p className="text-red-500 text-sm mt-1">{formErrors.phone}</p>
												)}
											</div>

											<div className="md:col-span-2">
												<Label htmlFor="specialRequests">Demandes spéciales (optionnel)</Label>
												<Textarea
													id="specialRequests"
													value={formData.specialRequests}
													onChange={(e) =>
														setFormData((prev) => ({
															...prev,
															specialRequests: e.target.value,
														}))
													}
													placeholder="Allergies, besoins particuliers, etc."
													rows={3}
												/>
											</div>
										</div>
									</CardContent>
								</Card>
							</motion.div>
						)}

						{/* Step 5: Payment */}
						{currentStep === 5 && (
							<motion.div
								key="step5"
								initial={{ opacity: 0, x: 50 }}
								animate={{ opacity: 1, x: 0 }}
								exit={{ opacity: 0, x: -50 }}
								transition={{ duration: 0.3 }}
							>
								<div className="text-center mb-8">
									<h2 className="text-3xl font-bold text-gray-900 mb-4">
										Confirmation de réservation
									</h2>
									<p className="text-gray-600">Finalisez votre réservation</p>
								</div>

								<div className="grid lg:grid-cols-2 gap-8">
									{/* Confirmation Notice */}
									<Card>
										<CardHeader>
											<CardTitle className="flex items-center gap-2">
												<CheckCircle className="w-5 h-5 text-emerald-500" />
												Confirmation de réservation
											</CardTitle>
										</CardHeader>
										<CardContent>
											<div className="space-y-4">
												<div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
													<h4 className="font-medium text-emerald-800 mb-2">
														Réservation sans paiement immédiat
													</h4>
													<p className="text-sm text-emerald-700">
														Votre réservation sera confirmée immédiatement. Le paiement se
														fera sur place le jour de votre excursion. Vous recevrez un
														email de confirmation avec tous les détails.
													</p>
												</div>

												<div className="space-y-3">
													<div className="flex items-center gap-2 text-sm text-gray-600">
														<CheckCircle className="w-4 h-4 text-emerald-500" />
														Confirmation immédiate par email
													</div>
													<div className="flex items-center gap-2 text-sm text-gray-600">
														<CheckCircle className="w-4 h-4 text-emerald-500" />
														Paiement sur place accepté
													</div>
													<div className="flex items-center gap-2 text-sm text-gray-600">
														<CheckCircle className="w-4 h-4 text-emerald-500" />
														Annulation gratuite 24h avant
													</div>
												</div>
											</div>
										</CardContent>
									</Card>

									{/* Order Summary */}
									<Card>
										<CardHeader>
											<CardTitle>Récapitulatif de commande</CardTitle>
										</CardHeader>
										<CardContent>
											<div className="space-y-4">
												{bookingItems.map((item, index) => (
													<div key={index} className="flex justify-between items-start">
														<div className="flex-1">
															<h4 className="font-medium">{item.service.name}</h4>
															<p className="text-sm text-gray-600">
																{formatDate(item.date)} à{" "}
																{new Date(item.timeSlot.start_time).toLocaleTimeString(
																	"fr-FR",
																	{
																		hour: "2-digit",
																		minute: "2-digit",
																	}
																)}
															</p>
															<p className="text-sm text-gray-600">
																{item.participants.length} participant
																{item.participants.length > 1 ? "s" : ""}
															</p>
														</div>
														<div className="text-right">
															<span className="font-semibold">{item.totalPrice}€</span>
														</div>
													</div>
												))}

												<Separator />

												<div className="flex justify-between items-center text-lg font-bold">
													<span>Total</span>
													<span className="text-emerald-600">{getTotalPrice()}€</span>
												</div>

												<div className="text-sm text-gray-600">
													<p>• Confirmation immédiate par email</p>
													<p>• Annulation gratuite jusqu'à 24h avant</p>
													<p>• Support client 7j/7</p>
												</div>
											</div>
										</CardContent>
									</Card>
								</div>
							</motion.div>
						)}
					</AnimatePresence>

					{/* Navigation Buttons */}
					<div className="flex justify-between items-center mt-8 pt-8 border-t">
						<Button
							variant="outline"
							onClick={() => setCurrentStep((prev) => Math.max(1, prev - 1))}
							disabled={currentStep === 1}
							className="flex items-center gap-2"
						>
							<ChevronLeft className="w-4 h-4" />
							Précédent
						</Button>

						{currentStep < 5 ? (
							<Button
								onClick={() => setCurrentStep((prev) => prev + 1)}
								disabled={!canProceedToNextStep()}
								className="flex items-center gap-2 bg-emerald-500 hover:bg-emerald-600"
							>
								Suivant
								<ChevronRight className="w-4 h-4" />
							</Button>
						) : (
							<Button
								onClick={handleSubmit}
								disabled={isLoading || !canProceedToNextStep()}
								className="flex items-center gap-2 bg-emerald-500 hover:bg-emerald-600"
							>
								{isLoading ? (
									<>
										<Loader2 className="w-4 h-4 animate-spin" />
										Traitement...
									</>
								) : (
									<>
										Finaliser la réservation
										<CreditCard className="w-4 h-4" />
									</>
								)}
							</Button>
						)}
					</div>
				</div>
			</div>
		</div>
	);
}
