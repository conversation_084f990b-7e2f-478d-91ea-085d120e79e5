import { useEffect, useState } from 'react'
import { supabase } from './supabase'

interface BusinessSetting {
  value: any
  description?: string
  type: string
  isPublic: boolean
}

interface UseSettingsReturn {
  settings: Record<string, any>
  categorized: Record<string, Record<string, BusinessSetting>>
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  updateSetting: (key: string, value: any) => Promise<boolean>
}

export function useSettings(category?: string, publicOnly = false): UseSettingsReturn {
  const [settings, setSettings] = useState<Record<string, any>>({})
  const [categorized, setCategorized] = useState<Record<string, Record<string, BusinessSetting>>>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchSettings = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams()
      if (category) params.append('category', category)
      if (publicOnly) params.append('public', 'true')

      const response = await fetch(`/api/settings?${params.toString()}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch settings')
      }

      const data = await response.json()
      setSettings(data.settings || {})
      setCategorized(data.categorized || {})

    } catch (err: any) {
      setError(err.message)
      console.error('Error fetching settings:', err)
    } finally {
      setLoading(false)
    }
  }

  const updateSetting = async (key: string, value: any): Promise<boolean> => {
    try {
      // Import adminApi dynamically to avoid circular dependency issues
      const { adminApi } = await import('./api-client')

      const result = await adminApi.updateSettings({ [key]: value })

      if (result.success) {
        // Update local state
        setSettings(prev => ({ ...prev, [key]: value }))
        return true
      } else {
        throw new Error(result.errors?.[0]?.error || 'Update failed')
      }

    } catch (err: any) {
      console.error('Error updating setting:', err)
      setError(err.message)
      return false
    }
  }

  useEffect(() => {
    fetchSettings()
  }, [category, publicOnly])

  return {
    settings,
    categorized,
    loading,
    error,
    refetch: fetchSettings,
    updateSetting
  }
}

// Hook for getting a single setting value
export function useSetting(key: string, defaultValue?: any) {
  const { settings, loading, error } = useSettings()
  
  return {
    value: settings[key] ?? defaultValue,
    loading,
    error
  }
}

// Hook for business info specifically
export function useBusinessInfo() {
  return useSettings('business_info', true)
}

// Hook for booking configuration
export function useBookingConfig() {
  return useSettings('booking')
}

// Utility function to get settings server-side
export async function getSettings(category?: string, publicOnly = false) {
  try {
    let query = supabase.from('business_settings').select('*')

    if (category) {
      query = query.eq('category', category)
    }

    if (publicOnly) {
      query = query.eq('is_public', true)
    }

    const { data: settings, error } = await query.order('category').order('key')

    if (error) {
      throw error
    }

    const settingsMap: Record<string, any> = {}
    
    settings?.forEach(setting => {
      let value = setting.value

      // Parse value based on type
      switch (setting.value_type) {
        case 'number':
          value = parseFloat(setting.value)
          break
        case 'boolean':
          value = setting.value === 'true'
          break
        case 'json':
          try {
            value = JSON.parse(setting.value)
          } catch {
            value = setting.value
          }
          break
        default:
          value = setting.value
      }

      settingsMap[setting.key] = value
    })

    return settingsMap

  } catch (error) {
    console.error('Error getting settings:', error)
    return {}
  }
}
