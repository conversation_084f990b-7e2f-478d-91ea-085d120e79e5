import { logAdminAction, withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { Database } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

type Customer = Database["public"]["Tables"]["customers"]["Row"];
type CustomerUpdate = Database["public"]["Tables"]["customers"]["Update"];

// GET /api/admin/customers - List all customers with admin details
export const GET = withAdminAuth(async (request: NextRequest) => {
	try {
		const { searchParams } = new URL(request.url);
		const page = parseInt(searchParams.get("page") || "1");
		const limit = parseInt(searchParams.get("limit") || "20");
		const search = searchParams.get("search");
		const hasReservations = searchParams.get("has_reservations");
		const marketingConsent = searchParams.get("marketing_consent");
		const offset = (page - 1) * limit;

		// Build query - customers table links to profiles via id
		let query = supabaseAdmin
			.from("customers")
			.select(
				`
        *,
        profile:profiles!customers_id_fkey (
          id,
          email,
          first_name,
          last_name,
          phone,
          role,
          created_at
        ),
        reservations (
          id,
          reservation_number,
          start_time,
          end_time,
          status,
          total_amount,
          participant_count,
          service:services (
            id,
            name,
            category
          )
        ),
        customer_feedback (
          id,
          rating,
          review_text,
          would_recommend,
          created_at
        )
      `
			)
			.order("created_at", { ascending: false })
			.range(offset, offset + limit - 1);

		// Apply filters
		if (marketingConsent !== null) {
			query = query.eq("marketing_consent", marketingConsent === "true");
		}

		const { data: customers, error } = await query;

		if (error) {
			console.error("Error fetching customers:", error);
			return NextResponse.json({ error: "Failed to fetch customers" }, { status: 500 });
		}

		// Filter by search and reservations after fetching due to join complexity
		let filteredCustomers = customers || [];

		if (search) {
			filteredCustomers = filteredCustomers.filter(
				(customer) =>
					customer.profile?.first_name?.toLowerCase().includes(search.toLowerCase()) ||
					customer.profile?.last_name?.toLowerCase().includes(search.toLowerCase()) ||
					customer.profile?.email?.toLowerCase().includes(search.toLowerCase()) ||
					customer.profile?.phone?.includes(search)
			);
		}

		if (hasReservations === "true") {
			filteredCustomers = filteredCustomers.filter(
				(customer) => customer.reservations && customer.reservations.length > 0
			);
		} else if (hasReservations === "false") {
			filteredCustomers = filteredCustomers.filter(
				(customer) => !customer.reservations || customer.reservations.length === 0
			);
		}

		// Enhance customers with statistics
		const enhancedCustomers = filteredCustomers.map((customer) => {
			const reservations = customer.reservations || [];
			const feedback = customer.customer_feedback || [];

			const totalSpent = reservations
				.filter((r) => r.status === "completed" || r.status === "confirmed")
				.reduce((sum, r) => sum + (r.total_amount || 0), 0);

			const avgRating =
				feedback.length > 0 ? feedback.reduce((sum, f) => sum + f.rating, 0) / feedback.length : null;

			return {
				...customer,
				stats: {
					totalReservations: reservations.length,
					totalSpent,
					completedReservations: reservations.filter((r) => r.status === "completed").length,
					cancelledReservations: reservations.filter((r) => r.status === "cancelled").length,
					upcomingReservations: reservations.filter(
						(r) => new Date(r.start_time) > new Date() && r.status !== "cancelled"
					).length,
					averageRating: avgRating,
					totalFeedback: feedback.length,
					lastReservation:
						reservations.length > 0
							? reservations.sort(
									(a, b) => new Date(b.start_time).getTime() - new Date(a.start_time).getTime()
							  )[0].start_time
							: null,
				},
			};
		});

		// Get total count for pagination
		const { count: totalCount } = await supabaseAdmin.from("customers").select("*", { count: "exact", head: true });

		return NextResponse.json({
			customers: enhancedCustomers,
			pagination: {
				page,
				limit,
				total: totalCount || 0,
				totalPages: Math.ceil((totalCount || 0) / limit),
			},
		});
	} catch (error) {
		console.error("Customers GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "customers:read");

// PUT /api/admin/customers - Bulk update customers
export const PUT = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const { customerIds, updates }: { customerIds: string[]; updates: CustomerUpdate } = await request.json();

		if (!customerIds || customerIds.length === 0) {
			return NextResponse.json({ error: "No customer IDs provided" }, { status: 400 });
		}

		// Get current customers for audit log
		const { data: currentCustomers } = await supabaseAdmin.from("customers").select("*").in("id", customerIds);

		// Update customers
		const { data: updatedCustomers, error } = await supabaseAdmin
			.from("customers")
			.update(updates)
			.in("id", customerIds)
			.select();

		if (error) {
			console.error("Error updating customers:", error);
			return NextResponse.json({ error: "Failed to update customers" }, { status: 500 });
		}

		// Log admin actions
		for (const customer of updatedCustomers || []) {
			const oldCustomer = currentCustomers?.find((c) => c.id === customer.id);
			await logAdminAction(user.id, "UPDATE", "customers", customer.id, oldCustomer, customer, request);
		}

		return NextResponse.json({
			customers: updatedCustomers,
			updated: updatedCustomers?.length || 0,
		});
	} catch (error) {
		console.error("Customers PUT error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "customers:write");

// POST /api/admin/customers/export - Export customer data
export const POST = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const { customerIds, format = "json" }: { customerIds?: string[]; format?: "json" | "csv" } =
			await request.json();

		// Build query
		let query = supabaseAdmin.from("customers").select(`
        *,
        profile:profiles!customers_id_fkey (
          email,
          first_name,
          last_name,
          phone,
          created_at
        ),
        reservations (
          reservation_number,
          start_time,
          status,
          total_amount,
          participant_count
        )
      `);

		if (customerIds && customerIds.length > 0) {
			query = query.in("id", customerIds);
		}

		const { data: customers, error } = await query;

		if (error) {
			console.error("Error exporting customers:", error);
			return NextResponse.json({ error: "Failed to export customers" }, { status: 500 });
		}

		// Log admin action
		await logAdminAction(
			user.id,
			"EXPORT",
			"customers",
			null,
			null,
			{ format, count: customers?.length || 0 },
			request
		);

		if (format === "csv") {
			// Convert to CSV format
			const csvHeaders = [
				"ID",
				"Email",
				"First Name",
				"Last Name",
				"Phone",
				"Date of Birth",
				"Nationality",
				"Emergency Contact",
				"Emergency Phone",
				"Dietary Restrictions",
				"Medical Conditions",
				"Marketing Consent",
				"Total Reservations",
				"Created At",
			];

			const csvRows =
				customers?.map((customer) => [
					customer.id,
					customer.profile?.email || "",
					customer.profile?.first_name || "",
					customer.profile?.last_name || "",
					customer.profile?.phone || "",
					customer.date_of_birth || "",
					customer.nationality || "",
					customer.emergency_contact_name || "",
					customer.emergency_contact_phone || "",
					customer.dietary_restrictions || "",
					customer.medical_conditions || "",
					customer.marketing_consent ? "Yes" : "No",
					customer.reservations?.length || 0,
					customer.created_at,
				]) || [];

			const csvContent = [csvHeaders, ...csvRows]
				.map((row) => row.map((field) => `"${field}"`).join(","))
				.join("\n");

			return new Response(csvContent, {
				headers: {
					"Content-Type": "text/csv",
					"Content-Disposition": `attachment; filename="customers-export-${
						new Date().toISOString().split("T")[0]
					}.csv"`,
				},
			});
		}

		return NextResponse.json({
			customers: customers || [],
			exportedAt: new Date().toISOString(),
			count: customers?.length || 0,
		});
	} catch (error) {
		console.error("Customer export error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "customers:read");
