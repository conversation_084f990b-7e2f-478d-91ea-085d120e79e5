export interface Database {
	public: {
		Tables: {
			services: {
				Row: {
					id: string;
					name: string;
					description: string | null;
					duration_minutes: number;
					base_price: number;
					max_participants: number;
					min_age: number;
					max_age: number | null;
					is_family_friendly: boolean;
					category: string | null;
					location: string | null;
					image_url: string | null;
					features: string[] | null;
					schedule: string[] | null;
					gallery: string[] | null;
					is_active: boolean;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					name: string;
					description?: string | null;
					duration_minutes: number;
					base_price: number;
					max_participants: number;
					category?: string | null;
					image_url?: string | null;
					features?: string[] | null;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					name?: string;
					description?: string | null;
					duration_minutes?: number;
					base_price?: number;
					max_participants?: number;
					category?: string | null;
					image_url?: string | null;
					features?: string[] | null;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
			};
			pricing_tiers: {
				Row: {
					id: string;
					service_id: string;
					tier_name: string;
					price: number;
					min_age: number;
					max_age: number | null;
					is_active: boolean;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					service_id: string;
					tier_name: string;
					price: number;
					min_age: number;
					max_age?: number | null;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					service_id?: string;
					tier_name?: string;
					price?: number;
					min_age?: number;
					max_age?: number | null;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
			};
			time_slots: {
				Row: {
					id: string;
					service_id: string;
					start_time: string;
					end_time: string;
					status: "available" | "booked" | "cancelled" | "completed";
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					service_id: string;
					start_time: string;
					end_time: string;
					status?: "available" | "booked" | "cancelled" | "completed";
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					service_id?: string;
					start_time?: string;
					end_time?: string;
					status?: "available" | "booked" | "cancelled" | "completed";
					created_at?: string;
					updated_at?: string;
				};
			};
			reservations: {
				Row: {
					id: string;
					service_id: string;
					customer_id: string;
					start_time: string;
					end_time: string;
					total_participants: number;
					total_amount: number;
					status: "pending" | "confirmed" | "cancelled" | "completed" | "no_show";
					special_requests: string | null;
					qr_code: string;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					service_id: string;
					customer_id: string;
					start_time: string;
					end_time: string;
					total_participants: number;
					total_amount: number;
					status?: "pending" | "confirmed" | "cancelled" | "completed" | "no_show";
					special_requests?: string | null;
					qr_code: string;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					service_id?: string;
					customer_id?: string;
					start_time?: string;
					end_time?: string;
					total_participants?: number;
					total_amount?: number;
					status?: "pending" | "confirmed" | "cancelled" | "completed" | "no_show";
					special_requests?: string | null;
					qr_code?: string;
					created_at?: string;
					updated_at?: string;
				};
			};
			equipment: {
				Row: {
					id: string;
					name: string;
					description: string | null;
					total_capacity: number;
					is_active: boolean;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					name: string;
					description?: string | null;
					total_capacity: number;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					name?: string;
					description?: string | null;
					total_capacity?: number;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
			};
			profiles: {
				Row: {
					id: string;
					email: string;
					first_name: string | null;
					last_name: string | null;
					phone: string | null;
					role: "admin" | "manager" | "employee" | "customer";
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id: string;
					email: string;
					first_name?: string | null;
					last_name?: string | null;
					phone?: string | null;
					role?: "admin" | "manager" | "employee" | "customer";
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					email?: string;
					first_name?: string | null;
					last_name?: string | null;
					phone?: string | null;
					role?: "admin" | "manager" | "employee" | "customer";
					created_at?: string;
					updated_at?: string;
				};
			};
			notifications: {
				Row: {
					id: string;
					recipient_id: string;
					reservation_id: string | null;
					notification_type: string;
					subject: string;
					content: string;
					priority: "low" | "medium" | "high";
					is_read: boolean;
					status: "pending" | "sent" | "failed";
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					recipient_id: string;
					reservation_id?: string | null;
					notification_type: string;
					subject: string;
					content: string;
					priority?: "low" | "medium" | "high";
					is_read?: boolean;
					status?: "pending" | "sent" | "failed";
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					recipient_id?: string;
					reservation_id?: string | null;
					notification_type?: string;
					subject?: string;
					content?: string;
					priority?: "low" | "medium" | "high";
					is_read?: boolean;
					status?: "pending" | "sent" | "failed";
					created_at?: string;
					updated_at?: string;
				};
			};
		};
	};
}
